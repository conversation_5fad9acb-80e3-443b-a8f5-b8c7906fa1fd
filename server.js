
const express = require('express');
const BigCommerce = require('node-bigcommerce');
const cors = require('cors');
const nodemailer = require('nodemailer');
const cron = require('node-cron');
require('dotenv').config();

const app = express();

app.use(express.json());
app.use(cors());
app.use(express.static('public'));

let notificationRequests = [];

// BigCommerce client for app mode
const bigCommerce = new BigCommerce({
  clientId: process.env.CLIENT_ID,
  secret: process.env.CLIENT_SECRET,
  callback: process.env.APP_URL + '/auth',
  responseType: 'json',
  apiVersion: 'v3'
});

// Store app installations
let installations = {};

// Email configuration
const emailTransporter = nodemailer.createTransport({
  service: 'gmail', // Changed back to gmail for better compatibility
  auth: {
    user: process.env.EMAIL_USER, // Your gmail address
    pass: process.env.EMAIL_PASS  // Your gmail app password
  }
});

// Function to send restock notification email
async function sendRestockEmail(email, productName, productUrl) {
  try {
    const mailOptions = {
      from: process.env.EMAIL_USER,
      to: email,
      subject: `${productName} is back in stock!`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #27ae60;">Good news! Your item is back in stock</h2>
          <p>Hi there!</p>
          <p>The item you were waiting for is now available:</p>
          <div style="border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 8px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">${productName}</h3>
            <p style="margin: 10px 0;">
              <a href="${productUrl}" style="background: #27ae60; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
                Shop Now
              </a>
            </p>
          </div>
          <p>Don't wait too long - items can sell out quickly!</p>
          <p>Happy shopping!</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="font-size: 12px; color: #666;">
            You received this email because you signed up for restock notifications on our website.
          </p>
        </div>
      `
    };

    await emailTransporter.sendMail(mailOptions);
    console.log(`Restock email sent to ${email} for product: ${productName}`);
    return true;
  } catch (error) {
    console.error(`Failed to send email to ${email}:`, error.message);
    return false;
  }
}

// Function to check for restocked products and send notifications
async function checkRestockedProducts() {
  console.log('Checking for restocked products...');

  // Get all pending notifications
  const pendingNotifications = notificationRequests.filter(req => !req.notified);

  if (pendingNotifications.length === 0) {
    console.log('No pending notifications to check');
    return;
  }

  console.log(`Checking ${pendingNotifications.length} pending notifications`);

  // Group notifications by product ID to avoid duplicate API calls
  const productGroups = {};
  pendingNotifications.forEach(notification => {
    if (!productGroups[notification.productId]) {
      productGroups[notification.productId] = [];
    }
    productGroups[notification.productId].push(notification);
  });

  // Check each product's stock status
  for (const productId of Object.keys(productGroups)) {
    try {
      const storeClient = new BigCommerce({
        clientId: process.env.CLIENT_ID,
        storeHash: process.env.STORE_HASH,
        accessToken: process.env.ACCESS_TOKEN,
        responseType: 'json',
        apiVersion: 'v3'
      });

      // Get product data
      const response = await storeClient.get(`/catalog/products/${productId}`);
      const product = response.data;

      // Check if product is back in stock (same logic as stock check API)
      let isInStock = false;

      try {
        const variantsResponse = await storeClient.get(`/catalog/products/${productId}/variants`);
        const variants = variantsResponse.data;

        if (variants.length > 0) {
          const totalVariantStock = variants.reduce((total, variant) => {
            return total + (variant.inventory_level || 0);
          }, 0);
          isInStock = totalVariantStock > 0;
        } else {
          isInStock = product.inventory_level > 0;
        }
      } catch (variantError) {
        isInStock = product.inventory_level > 0;
      }

      if (isInStock) {
        console.log(`Product ${productId} (${product.name}) is back in stock!`);

        // Send emails to all subscribers for this product
        const notifications = productGroups[productId];
        const productUrl = `https://${process.env.STORE_HASH}.mybigcommerce.com${product.custom_url.url}`;

        for (const notification of notifications) {
          const emailSent = await sendRestockEmail(
            notification.email,
            product.name,
            productUrl
          );

          if (emailSent) {
            // Mark as notified
            notification.notified = true;
            notification.notifiedAt = new Date();
            console.log(`Marked notification as sent for ${notification.email}`);
          }
        }
      } else {
        console.log(`Product ${productId} (${product.name}) still out of stock`);
      }

    } catch (error) {
      console.error(`Error checking stock for product ${productId}:`, error.message);
    }
  }

  console.log('Finished checking restocked products');
}

// App installation endpoint
app.get('/auth', async (req, res) => {
  try {
    const data = await bigCommerce.authorize(req.query);
    
    // Store installation data
    installations[data.context] = {
      accessToken: data.access_token,
      storeHash: data.context.split('/')[1],
      scope: data.scope,
      installedAt: new Date()
    };
    
    console.log('App installed for store:', data.context);
    
    // Automatically install script in store
    await installScriptInStore(data.access_token, data.context.split('/')[1]);
    
    res.send(`
      <h1>✅ Stock Notification App Installed!</h1>
      <p>Your app is now active and will automatically show notification forms on out-of-stock products.</p>
      <p><a href="/dashboard">Go to Dashboard</a></p>
    `);
  } catch (error) {
    console.error('Installation error:', error);
    res.status(500).send('Installation failed: ' + error.message);
  }
});

// Automatically install script in store
async function installScriptInStore(accessToken, storeHash) {
  try {
    const storeClient = new BigCommerce({
      clientId: process.env.CLIENT_ID,
      storeHash: storeHash,
      accessToken: accessToken,
      responseType: 'json',
      apiVersion: 'v3'
    });
    
    const scriptData = {
      name: 'Stock Notification App',
      description: 'Shows notification forms for out-of-stock products',
      src: `${process.env.APP_URL}/stock-notification.js`,
      auto_uninstall: true,
      load_method: 'default',
      location: 'footer',
      visibility: 'storefront',
      kind: 'src'
    };
    
    const result = await storeClient.post('/content/scripts', scriptData);
    console.log('Script automatically installed:', result.data.uuid);
    
    return result.data;
    
  } catch (error) {
    console.error('Failed to auto-install script:', error);
  }
}

// App loading endpoint
app.get('/load', (req, res) => {
  try {
    const data = bigCommerce.verify(req.query.signed_payload);
    res.redirect('/dashboard?store=' + data.store_hash);
  } catch (error) {
    res.status(401).send('Unauthorized');
  }
});

// App dashboard
app.get('/dashboard', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>Stock Notification Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .stats { display: flex; gap: 20px; margin: 20px 0; }
            .stat-card { padding: 20px; border: 1px solid #ddd; border-radius: 8px; flex: 1; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
            th { background: #f5f5f5; }
        </style>
    </head>
    <body>
        <h1>📦 Stock Notification Dashboard</h1>
        
        <div class="stats">
            <div class="stat-card">
                <h3>Pending Notifications</h3>
                <p id="active-count">Loading...</p>
            </div>
            <div class="stat-card">
                <h3>App Status</h3>
                <p style="color: green;">✅ Active</p>
            </div>
        </div>
        
        <div>
            <h3>Notification Requests</h3>
            <div id="notifications-table">Loading...</div>
        </div>
        
        <script>
            fetch('/api/notifications')
                .then(res => res.json())
                .then(data => {
                    document.getElementById('active-count').textContent = data.length;
                    
                    if (data.length === 0) {
                        document.getElementById('notifications-table').innerHTML = '<p>No pending notifications</p>';
                    } else {
                        let tableHTML = '<table><tr><th>Email</th><th>Product ID</th><th>Created</th></tr>';
                        data.forEach(notification => {
                            tableHTML += \`<tr>
                                <td>\${notification.email}</td>
                                <td>\${notification.productId}</td>
                                <td>\${new Date(notification.createdAt).toLocaleDateString()}</td>
                            </tr>\`;
                        });
                        tableHTML += '</table>';
                        document.getElementById('notifications-table').innerHTML = tableHTML;
                    }
                });
        </script>
    </body>
    </html>
  `);
});

// Main script that gets injected into storefront
app.get('/stock-notification.js', (req, res) => {
  // Get the app URL from environment
  const appUrl = process.env.APP_URL;
  console.log('Using APP_URL:', appUrl);

  res.setHeader('Content-Type', 'application/javascript');
  res.send(`
    (function() {
      console.log("Stock notification script loaded!");
      console.log("Current URL:", window.location.pathname);

      // Only run on product pages - check multiple possible URL patterns
      const isProductPage =
        window.location.pathname.includes('/products/') ||
        window.location.pathname.includes('/product/') ||
        window.location.pathname.includes('/p/') ||
        window.location.pathname.includes('/out-of-stock/') ||
        document.querySelector('[data-product-id]') !== null;

      if (!isProductPage) {
        console.log("Not a product page, exiting");
        return;
      }

      console.log("This is a product page, continuing...");

      // App URL hardcoded for API calls
      const APP_URL = "${appUrl}";
      console.log("Stock notification app URL:", APP_URL);

      function getProductId() {
        console.log("Detecting product ID...");

        // Try multiple URL patterns
        let urlMatch = window.location.pathname.match(/\\/products\\/.*?(\\d+)/);
        if (urlMatch) {
          console.log("Found product ID in /products/ URL:", urlMatch[1]);
          return urlMatch[1];
        }

        urlMatch = window.location.pathname.match(/\\/product\\/.*?(\\d+)/);
        if (urlMatch) {
          console.log("Found product ID in /product/ URL:", urlMatch[1]);
          return urlMatch[1];
        }

        urlMatch = window.location.pathname.match(/\\/p\\/(\\d+)/);
        if (urlMatch) {
          console.log("Found product ID in /p/ URL:", urlMatch[1]);
          return urlMatch[1];
        }

        urlMatch = window.location.pathname.match(/\\/out-of-stock\\/(\\d+)/);
        if (urlMatch) {
          console.log("Found product ID in /out-of-stock/ URL:", urlMatch[1]);
          return urlMatch[1];
        }

        // Try to find product ID in data attributes - prioritize main product
        const productElements = document.querySelectorAll('[data-product-id]');
        console.log("Found", productElements.length, "elements with data-product-id");

        // Priority order for finding the main product ID:
        // 1. Main product section with data-entity-id (highest priority)
        const productView = document.querySelector('.productView[data-entity-id]');
        if (productView) {
          console.log("Found main product ID in .productView data-entity-id:", productView.dataset.entityId);
          return productView.dataset.entityId;
        }

        // 2. Other entity-id selectors
        const entitySelectors = [
          '[data-entity-id]',
          '.product-single[data-entity-id]',
          '.product-details[data-entity-id]',
          '.product-main[data-entity-id]'
        ];

        for (let selector of entitySelectors) {
          const element = document.querySelector(selector);
          if (element && !element.closest('.related-products, .product-recommendations, .similar-products, [class*="related"], [class*="recommendation"], [class*="carousel"], .product-list, .products-grid')) {
            console.log(\`Found main product ID in \${selector} data-entity-id:\`, element.dataset.entityId);
            return element.dataset.entityId;
          }
        }

        // 3. Product form (fallback for data-product-id)
        const productForm = document.querySelector('form[data-product-id]');
        if (productForm) {
          console.log("Found main product ID in form:", productForm.dataset.productId);
          return productForm.dataset.productId;
        }

        // 4. Product view container with data-product-id (fallback)
        const productViewSelectors = [
          '.productView[data-product-id]',
          '.product-single[data-product-id]',
          '.product-details[data-product-id]',
          '.product-main[data-product-id]',
          '.product-info[data-product-id]',
          '.product-content[data-product-id]',
          'main [data-product-id]',
          '.main-content [data-product-id]'
        ];

        for (let selector of productViewSelectors) {
          const productViewEl = document.querySelector(selector);
          if (productViewEl) {
            console.log(\`Found main product ID in \${selector}:\`, productViewEl.dataset.productId);
            return productViewEl.dataset.productId;
          }
        }

        // 3. First element that's not in related products section
        for (let el of productElements) {
          const isInRelated = el.closest('.related-products, .product-recommendations, .similar-products, [class*="related"], [class*="recommendation"], [class*="carousel"], .product-list, .products-grid');
          console.log(\`Checking element with product ID \${el.dataset.productId}, isInRelated: \${!!isInRelated}\`);
          if (isInRelated) {
            console.log(\`Skipping product ID \${el.dataset.productId} - found in related section: \${isInRelated.className}\`);
          } else {
            console.log("Found main product ID (not in related section):", el.dataset.productId);
            return el.dataset.productId;
          }
        }

        // 4. Fallback to first element
        if (productElements.length > 0) {
          const productId = productElements[0].dataset.productId;
          console.log("Using first data-product-id as fallback:", productId);
          return productId;
        }

        // Try to find product ID in meta tags
        console.log("Checking meta tags...");
        const metaProduct = document.querySelector('meta[property="product:retailer_item_id"]');
        if (metaProduct) {
          console.log("Found product ID in meta tag:", metaProduct.content);
          return metaProduct.content;
        }

        // Try other common selectors
        console.log("Checking other common selectors...");
        // (productForm check moved above for priority)

        console.log("No product ID found anywhere");
        return null;
      }

      function addNotificationForm() {
        const formHTML = \`
          <div id="stock-notification-form" style="margin: 20px 0; padding: 20px; border: 2px solid #e74c3c; border-radius: 8px; background: #fdf2f2; float: left;">
            <h4 style="margin: 0 0 10px 0; color: #e74c3c;">🔔 Out of Stock</h4>
            <p style="margin: 0 0 15px 0; color: #666;">Get notified when this item is back in stock!</p>
            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
              <input type="email"
                     id="notification-email"
                     placeholder="Enter your email address"
                     style="padding: 10px 15px; border: 1px solid #ddd; border-radius: 4px; flex: 1; min-width: 250px; max-width: 350px;">
              <button onclick="subscribeToNotification()"
                      style="padding: 10px 20px; background: #e74c3c; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                Notify Me
              </button>
            </div>
            <div id="notification-result" style="margin-top: 10px;"></div>
          </div>
        \`;

        // Try to find the best place to insert the form
        const insertTargets = [
          '.product-options',
          '.product-form',
          '.product-details',
          '.product-single',
          '.productView-details',
          '.product-info'
        ];

        console.log('Looking for insertion targets...');
        for (const selector of insertTargets) {
          const target = document.querySelector(selector);
          console.log('Checking selector:', selector, 'Found:', !!target);
          if (target) {
            console.log('Inserting form after:', selector);
            target.insertAdjacentHTML('afterend', formHTML);
            return true;
          }
        }

        // Fallback: insert after product title
        console.log('Trying fallback: insert after h1');
        const title = document.querySelector('h1');
        if (title) {
          console.log('Found h1, inserting form after title');
          title.insertAdjacentHTML('afterend', formHTML);
          return true;
        }

        console.log('No suitable insertion point found');
        return false;
      }

      window.subscribeToNotification = function() {
        const email = document.getElementById('notification-email').value;
        const resultDiv = document.getElementById('notification-result');
        const productId = getProductId();

        if (!email || !email.includes('@')) {
          resultDiv.innerHTML = '<p style="color: #e74c3c; margin: 5px 0;">Please enter a valid email address</p>';
          return;
        }

        resultDiv.innerHTML = '<p style="color: #666; margin: 5px 0;">Processing...</p>';

        fetch(APP_URL + '/api/notify/' + productId, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'ngrok-skip-browser-warning': 'true'
          },
          body: JSON.stringify({ email: email })
        })
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            resultDiv.innerHTML = '<p style="color: #27ae60; margin: 5px 0;">✅ ' + data.message + '</p>';
            document.getElementById('notification-email').value = '';
          } else {
            resultDiv.innerHTML = '<p style="color: #f39c12; margin: 5px 0;">⚠️ ' + data.message + '</p>';
          }
        })
        .catch(error => {
          resultDiv.innerHTML = '<p style="color: #e74c3c; margin: 5px 0;">❌ Error: Please try again</p>';
        });
      };

      function checkStockAndShowForm() {
        const productId = getProductId();
        console.log('Product ID found:', productId);
        if (!productId) {
          console.log('No product ID found, not showing form');
          return;
        }

        // Check stock status before showing form
        console.log('Checking stock for product:', productId);
        // Use real stock check endpoint
        const stockUrl = APP_URL + '/api/check-stock/' + productId;
        console.log('Stock check URL:', stockUrl);

        fetch(stockUrl, {
          headers: {
            'ngrok-skip-browser-warning': 'true'
          }
        })
          .then(res => {
            console.log('Stock check response status:', res.status);
            if (!res.ok) {
              throw new Error('HTTP ' + res.status + ': ' + res.statusText);
            }
            return res.text(); // Get as text first to see what we're getting
          })
          .then(text => {
            console.log('Raw response:', text);
            try {
              const data = JSON.parse(text);
              console.log('Parsed stock check response:', data);
              if (data.showNotificationForm && data.isOutOfStock) {
                console.log('Product is out of stock, showing notification form');
                const formAdded = addNotificationForm();
                console.log('Form added successfully:', formAdded);
              } else {
                console.log('Product is in stock, not showing form');
              }
            } catch (parseError) {
              console.error('Failed to parse JSON response:', parseError);
              console.log('Response was not valid JSON, showing form anyway');
              const formAdded = addNotificationForm();
              console.log('Form added successfully:', formAdded);
            }
          })
          .catch(error => {
            console.error('Stock check failed:', error);
            console.log('Error occurred, showing form anyway');
            const formAdded = addNotificationForm();
            console.log('Form added successfully:', formAdded);
          });
      }

      // Initialize when page loads
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkStockAndShowForm);
      } else {
        checkStockAndShowForm();
      }
    })();
  `);
});

// Uninstall endpoint
app.get('/uninstall', (req, res) => {
  try {
    const data = bigCommerce.verify(req.query.signed_payload);
    delete installations[data.store_hash];
    console.log('App uninstalled from store:', data.store_hash);
    res.send('App uninstalled successfully');
  } catch (error) {
    res.status(401).send('Unauthorized');
  }
});

// Test endpoint to force out of stock for specific product
app.get('/api/force-out-of-stock/:productId', (req, res) => {
  const productId = req.params.productId;
  console.log('Forcing out of stock for product:', productId);
  res.json({
    productId: productId,
    productName: 'Test Product (Forced Out of Stock)',
    stockLevel: 0,
    isOutOfStock: true,
    showNotificationForm: true
  });
});

// API endpoint to check stock status
app.get('/api/check-stock/:productId', async (req, res) => {
  try {
    const productId = req.params.productId;
    console.log('Stock check requested for product ID:', productId);

    const storeClient = new BigCommerce({
      clientId: process.env.CLIENT_ID,
      storeHash: process.env.STORE_HASH,
      accessToken: process.env.ACCESS_TOKEN,
      responseType: 'json',
      apiVersion: 'v3'
    });

    console.log('Making API call to BigCommerce for product:', productId);
    const response = await storeClient.get(`/catalog/products/${productId}`);
    const product = response.data;

    console.log('Product inventory tracking:', product.inventory_tracking);
    console.log('Product inventory level:', product.inventory_level);
    console.log('Product availability:', product.availability);
    console.log('Product type:', product.type);

    // Check variants to determine actual stock status
    let isOutOfStock = false;
    let effectiveStockLevel = product.inventory_level;

    try {
      const variantsResponse = await storeClient.get(`/catalog/products/${productId}/variants`);
      const variants = variantsResponse.data;
      console.log(`Found ${variants.length} variants`);

      if (variants.length > 0) {
        // Product has variants - check variant inventory levels
        const totalVariantStock = variants.reduce((total, variant) => {
          console.log(`Variant ${variant.id} (${variant.sku}): ${variant.inventory_level} units`);
          return total + (variant.inventory_level || 0);
        }, 0);

        effectiveStockLevel = totalVariantStock;
        isOutOfStock = totalVariantStock <= 0;
        console.log(`Total variant stock: ${totalVariantStock}, out of stock: ${isOutOfStock}`);

        // Special case: if inventory tracking is "product" level but all variants are 0,
        // and main product shows high stock, this might be a BigCommerce quirk
        if (product.inventory_tracking === "product" && totalVariantStock === 0 && product.inventory_level > 1000) {
          console.log("Detected possible BigCommerce inventory quirk - treating as out of stock");
          isOutOfStock = true;
          effectiveStockLevel = 0;
        }
      } else {
        // No variants - use main product inventory
        isOutOfStock = product.inventory_level <= 0;
        effectiveStockLevel = product.inventory_level;
        console.log(`No variants, using main product stock: ${product.inventory_level}, out of stock: ${isOutOfStock}`);
      }
    } catch (variantError) {
      console.log('Error getting variants, using main product inventory:', variantError.message);
      isOutOfStock = product.inventory_level <= 0;
    }

    const result = {
      productId: productId,
      productName: product.name,
      stockLevel: effectiveStockLevel,
      isOutOfStock: isOutOfStock,
      showNotificationForm: isOutOfStock
    };

    console.log('Sending stock check response:', result);
    res.json(result);

  } catch (error) {
    console.error('Stock check error for product', req.params.productId, ':', error.message);
    const errorResult = {
      error: 'Failed to check stock',
      showNotificationForm: true,
      productId: req.params.productId
    };
    console.log('Sending error response:', errorResult);
    res.json(errorResult);
  }
});

// API endpoint to subscribe to notifications
app.post('/api/notify/:productId', async (req, res) => {
  try {
    const { email } = req.body;
    const productId = req.params.productId;
    
    if (!email || !email.includes('@')) {
      return res.json({ success: false, message: 'Valid email is required' });
    }
    
    // Check if already subscribed
    const existing = notificationRequests.find(
      req => req.email === email && req.productId === productId
    );
    
    if (existing) {
      return res.json({ 
        success: false, 
        message: 'You are already subscribed to notifications for this product' 
      });
    }
    
    // Add to notification list
    notificationRequests.push({
      email: email,
      productId: productId,
      createdAt: new Date(),
      notified: false
    });
    
    console.log(`New notification request: ${email} for product ${productId}`);
    
    res.json({
      success: true,
      message: 'Thank you! You\'ll be notified when this item is available.'
    });
    
  } catch (error) {
    console.error('Notification subscription error:', error);
    res.json({ success: false, message: 'Failed to subscribe. Please try again.' });
  }
});

// API endpoint to get all notifications
app.get('/api/notifications', (req, res) => {
  res.json(notificationRequests);
});

// Manual trigger for checking restocked products (for testing)
app.post('/api/check-restock', async (req, res) => {
  console.log('Manual restock check triggered');
  try {
    await checkRestockedProducts();
    res.json({ success: true, message: 'Restock check completed' });
  } catch (error) {
    console.error('Manual restock check failed:', error);
    res.json({ success: false, message: 'Restock check failed: ' + error.message });
  }
});

// Schedule automatic stock checking every 30 seconds (for testing)
cron.schedule('*/30 * * * * *', () => {
  checkRestockedProducts();
});

// Also check immediately when server starts (after 1 minute)
setTimeout(() => {
  checkRestockedProducts();
}, 60000);

app.listen(3000, () => {
  console.log('🚀 Stock Notification App running on http://localhost:3000');
  console.log('📱 App URL:', process.env.APP_URL);
  console.log('🏪 Store Hash:', process.env.STORE_HASH);
  console.log('📧 Email notifications: Enabled');
  console.log('⏰ Stock checking: Every 30 seconds (testing mode)');
});



