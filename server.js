
const express = require('express');
const BigCommerce = require('node-bigcommerce');
const cors = require('cors');
require('dotenv').config();

const app = express();

app.use(express.json());
app.use(cors());
app.use(express.static('public'));

let notificationRequests = [];

// BigCommerce client for app mode
const bigCommerce = new BigCommerce({
  clientId: process.env.CLIENT_ID,
  secret: process.env.CLIENT_SECRET,
  callback: process.env.APP_URL + '/auth',
  responseType: 'json',
  apiVersion: 'v3'
});

// Store app installations
let installations = {};

// App installation endpoint
app.get('/auth', async (req, res) => {
  try {
    const data = await bigCommerce.authorize(req.query);
    
    // Store installation data
    installations[data.context] = {
      accessToken: data.access_token,
      storeHash: data.context.split('/')[1],
      scope: data.scope,
      installedAt: new Date()
    };
    
    console.log('App installed for store:', data.context);
    
    // Automatically install script in store
    await installScriptInStore(data.access_token, data.context.split('/')[1]);
    
    res.send(`
      <h1>✅ Stock Notification App Installed!</h1>
      <p>Your app is now active and will automatically show notification forms on out-of-stock products.</p>
      <p><a href="/dashboard">Go to Dashboard</a></p>
    `);
  } catch (error) {
    console.error('Installation error:', error);
    res.status(500).send('Installation failed: ' + error.message);
  }
});

// Automatically install script in store
async function installScriptInStore(accessToken, storeHash) {
  try {
    const storeClient = new BigCommerce({
      clientId: process.env.CLIENT_ID,
      storeHash: storeHash,
      accessToken: accessToken,
      responseType: 'json',
      apiVersion: 'v3'
    });
    
    const scriptData = {
      name: 'Stock Notification App',
      description: 'Shows notification forms for out-of-stock products',
      src: `${process.env.APP_URL}/stock-notification.js`,
      auto_uninstall: true,
      load_method: 'default',
      location: 'footer',
      visibility: 'storefront',
      kind: 'src'
    };
    
    const result = await storeClient.post('/content/scripts', scriptData);
    console.log('Script automatically installed:', result.data.uuid);
    
    return result.data;
    
  } catch (error) {
    console.error('Failed to auto-install script:', error);
  }
}

// App loading endpoint
app.get('/load', (req, res) => {
  try {
    const data = bigCommerce.verify(req.query.signed_payload);
    res.redirect('/dashboard?store=' + data.store_hash);
  } catch (error) {
    res.status(401).send('Unauthorized');
  }
});

// App dashboard
app.get('/dashboard', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html>
    <head>
        <title>Stock Notification Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .stats { display: flex; gap: 20px; margin: 20px 0; }
            .stat-card { padding: 20px; border: 1px solid #ddd; border-radius: 8px; flex: 1; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
            th { background: #f5f5f5; }
        </style>
    </head>
    <body>
        <h1>📦 Stock Notification Dashboard</h1>
        
        <div class="stats">
            <div class="stat-card">
                <h3>Pending Notifications</h3>
                <p id="active-count">Loading...</p>
            </div>
            <div class="stat-card">
                <h3>App Status</h3>
                <p style="color: green;">✅ Active</p>
            </div>
        </div>
        
        <div>
            <h3>Notification Requests</h3>
            <div id="notifications-table">Loading...</div>
        </div>
        
        <script>
            fetch('/api/notifications')
                .then(res => res.json())
                .then(data => {
                    document.getElementById('active-count').textContent = data.length;
                    
                    if (data.length === 0) {
                        document.getElementById('notifications-table').innerHTML = '<p>No pending notifications</p>';
                    } else {
                        let tableHTML = '<table><tr><th>Email</th><th>Product ID</th><th>Created</th></tr>';
                        data.forEach(notification => {
                            tableHTML += \`<tr>
                                <td>\${notification.email}</td>
                                <td>\${notification.productId}</td>
                                <td>\${new Date(notification.createdAt).toLocaleDateString()}</td>
                            </tr>\`;
                        });
                        tableHTML += '</table>';
                        document.getElementById('notifications-table').innerHTML = tableHTML;
                    }
                });
        </script>
    </body>
    </html>
  `);
});

// Main script that gets injected into storefront
app.get('/stock-notification.js', (req, res) => {
  // Get the app URL from environment
  const appUrl = process.env.APP_URL;
  console.log('Using APP_URL:', appUrl);

  res.setHeader('Content-Type', 'application/javascript');
  res.send(`
    (function() {
      // Only run on product pages
      if (!window.location.pathname.includes('/products/')) return;

      // App URL hardcoded for API calls
      const APP_URL = "${appUrl}";
      console.log("Stock notification app URL:", APP_URL);

      function getProductId() {
        const urlMatch = window.location.pathname.match(/\\/products\\/.*?(\\d+)/);
        if (urlMatch) return urlMatch[1];

        const productElement = document.querySelector('[data-product-id]');
        if (productElement) return productElement.dataset.productId;

        return null;
      }

      function addNotificationForm() {
        const formHTML = \`
          <div id="stock-notification-form" style="margin: 20px 0; padding: 20px; border: 2px solid #e74c3c; border-radius: 8px; background: #fdf2f2;">
            <h4 style="margin: 0 0 10px 0; color: #e74c3c;">🔔 Out of Stock</h4>
            <p style="margin: 0 0 15px 0; color: #666;">Get notified when this item is back in stock!</p>
            <div style="display: flex; gap: 10px; align-items: center; flex-wrap: wrap;">
              <input type="email"
                     id="notification-email"
                     placeholder="Enter your email address"
                     style="padding: 10px 15px; border: 1px solid #ddd; border-radius: 4px; flex: 1; min-width: 250px; max-width: 350px;">
              <button onclick="subscribeToNotification()"
                      style="padding: 10px 20px; background: #e74c3c; color: white; border: none; border-radius: 4px; cursor: pointer; font-weight: bold;">
                Notify Me
              </button>
            </div>
            <div id="notification-result" style="margin-top: 10px;"></div>
          </div>
        \`;

        // Try to find the best place to insert the form
        const insertTargets = [
          '.product-options',
          '.product-form',
          '.product-details',
          '.product-single',
          '.productView-details',
          '.product-info'
        ];

        for (const selector of insertTargets) {
          const target = document.querySelector(selector);
          if (target) {
            target.insertAdjacentHTML('afterend', formHTML);
            return true;
          }
        }

        // Fallback: insert after product title
        const title = document.querySelector('h1');
        if (title) {
          title.insertAdjacentHTML('afterend', formHTML);
          return true;
        }

        return false;
      }

      window.subscribeToNotification = function() {
        const email = document.getElementById('notification-email').value;
        const resultDiv = document.getElementById('notification-result');
        const productId = getProductId();

        if (!email || !email.includes('@')) {
          resultDiv.innerHTML = '<p style="color: #e74c3c; margin: 5px 0;">Please enter a valid email address</p>';
          return;
        }

        resultDiv.innerHTML = '<p style="color: #666; margin: 5px 0;">⏳ Subscribing...</p>';

        fetch(APP_URL + '/api/notify/' + productId, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ email: email })
        })
        .then(res => res.json())
        .then(data => {
          if (data.success) {
            resultDiv.innerHTML = '<p style="color: #27ae60; margin: 5px 0;">✅ ' + data.message + '</p>';
            document.getElementById('notification-email').value = '';
          } else {
            resultDiv.innerHTML = '<p style="color: #f39c12; margin: 5px 0;">⚠️ ' + data.message + '</p>';
          }
        })
        .catch(error => {
          resultDiv.innerHTML = '<p style="color: #e74c3c; margin: 5px 0;">❌ Error: Please try again</p>';
        });
      };

      function checkStockAndShowForm() {
        const productId = getProductId();
        console.log('Product ID found:', productId);
        if (!productId) {
          console.log('No product ID found, not showing form');
          return;
        }

        // For testing: always show the form
        console.log('Adding notification form for product:', productId);
        addNotificationForm();

        // Original code (commented out for testing):
        /*
        fetch(APP_URL + '/api/check-stock/' + productId)
          .then(res => res.json())
          .then(data => {
            if (data.showNotificationForm && data.isOutOfStock) {
              addNotificationForm();
            }
          })
          .catch(error => console.error('Stock check failed:', error));
        */
      }

      // Initialize when page loads
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkStockAndShowForm);
      } else {
        checkStockAndShowForm();
      }
    })();
  `);
});

// Uninstall endpoint
app.get('/uninstall', (req, res) => {
  try {
    const data = bigCommerce.verify(req.query.signed_payload);
    delete installations[data.store_hash];
    console.log('App uninstalled from store:', data.store_hash);
    res.send('App uninstalled successfully');
  } catch (error) {
    res.status(401).send('Unauthorized');
  }
});

// API endpoint to check stock status
app.get('/api/check-stock/:productId', async (req, res) => {
  try {
    const productId = req.params.productId;

    const storeClient = new BigCommerce({
      clientId: process.env.CLIENT_ID,
      storeHash: process.env.STORE_HASH,
      accessToken: process.env.ACCESS_TOKEN,
      responseType: 'json',
      apiVersion: 'v3'
    });
    
    const response = await storeClient.get(`/catalog/products/${productId}`);
    const product = response.data;
    
    const isOutOfStock = product.inventory_level <= 0;
    
    res.json({
      productId: productId,
      productName: product.name,
      stockLevel: product.inventory_level,
      isOutOfStock: isOutOfStock,
      showNotificationForm: isOutOfStock
    });
    
  } catch (error) {
    console.error('Stock check error:', error);
    res.json({ 
      error: 'Failed to check stock',
      showNotificationForm: true
    });
  }
});

// API endpoint to subscribe to notifications
app.post('/api/notify/:productId', async (req, res) => {
  try {
    const { email } = req.body;
    const productId = req.params.productId;
    
    if (!email || !email.includes('@')) {
      return res.json({ success: false, message: 'Valid email is required' });
    }
    
    // Check if already subscribed
    const existing = notificationRequests.find(
      req => req.email === email && req.productId === productId
    );
    
    if (existing) {
      return res.json({ 
        success: false, 
        message: 'You are already subscribed to notifications for this product' 
      });
    }
    
    // Add to notification list
    notificationRequests.push({
      email: email,
      productId: productId,
      createdAt: new Date(),
      notified: false
    });
    
    console.log(`New notification request: ${email} for product ${productId}`);
    
    res.json({ 
      success: true, 
      message: 'Successfully subscribed! We will notify you when this item is back in stock.' 
    });
    
  } catch (error) {
    console.error('Notification subscription error:', error);
    res.json({ success: false, message: 'Failed to subscribe. Please try again.' });
  }
});

// API endpoint to get all notifications
app.get('/api/notifications', (req, res) => {
  res.json(notificationRequests);
});

app.listen(3000, () => {
  console.log('🚀 Stock Notification App running on http://localhost:3000');
  console.log('📱 App URL:', process.env.APP_URL);
  console.log('🏪 Store Hash:', process.env.STORE_HASH);
});



