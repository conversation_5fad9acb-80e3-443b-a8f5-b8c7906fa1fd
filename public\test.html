<!DOCTYPE html>
<html>
<head>
    <title>Stock Notification Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .product-test { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        input { padding: 8px; margin-right: 10px; width: 250px; }
        button { padding: 8px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .result { margin-top: 10px; padding: 10px; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>🔔 Stock Notification Test</h1>
    
    <div class="product-test">
        <h3>Product 134 - New York City Ringer T-Shirt</h3>
        <p>Stock Level: <span id="stock-134">Loading...</span></p>
        
        <div>
            <input type="email" id="email-134" placeholder="Enter your email for notifications">
            <button onclick="subscribeToStock(134)">Notify Me When Available</button>
        </div>
        <div id="result-134"></div>
    </div>

    <div class="product-test">
        <h3>Product 115 - I Love NY T-Shirt (Out of Stock)</h3>
        <p>Stock Level: <span id="stock-115">Loading...</span></p>
        
        <div>
            <input type="email" id="email-115" placeholder="Enter your email for notifications">
            <button onclick="subscribeToStock(115)">Notify Me When Available</button>
        </div>
        <div id="result-115"></div>
    </div>

    <script>
        // Check stock levels
        [134, 115].forEach(productId => {
            fetch(`/api/product/${productId}/stock`)
                .then(res => res.json())
                .then(data => {
                    document.getElementById(`stock-${productId}`).textContent = 
                        `${data.stock_level} (${data.is_available ? 'In Stock' : 'Out of Stock'})`;
                })
                .catch(error => {
                    document.getElementById(`stock-${productId}`).textContent = 'Error loading';
                });
        });

        function subscribeToStock(productId) {
            const email = document.getElementById(`email-${productId}`).value;
            const resultDiv = document.getElementById(`result-${productId}`);
            
            if (!email) {
                resultDiv.innerHTML = '<div class="result error">Please enter your email</div>';
                return;
            }
            
            fetch(`/api/notify/${productId}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ email: email })
            })
            .then(res => res.json())
            .then(data => {
                const className = data.success ? 'success' : 'error';
                resultDiv.innerHTML = `<div class="result ${className}">${data.message}</div>`;
                if (data.success) {
                    document.getElementById(`email-${productId}`).value = '';
                }
            })
            .catch(error => {
                resultDiv.innerHTML = '<div class="result error">Error: Please try again</div>';
            });
        }
    </script>
</body>
</html>